# =============================================================================
# CMDB Dockerfile
# =============================================================================
# 
# 多阶段构建 Dockerfile，用于构建和运行 CMDB 应用
# 
# 构建命令:
#   docker build -t cmdb:latest .
#   docker build --build-arg VERSION=1.0.0 -t cmdb:1.0.0 .
#
# 运行命令:
#   docker run --rm -p 8080:8080 cmdb:latest
#
# =============================================================================

# -----------------------------------------------------------------------------
# 构建阶段
# -----------------------------------------------------------------------------
FROM golang:1.24.3-alpine3.21 AS builder

# 设置构建参数
ARG VERSION=dev
ARG GOPROXY=https://goproxy.cn,direct

# 安装必要的工具
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && apk add --no-cache git ca-certificates tzdata

# 设置工作目录
WORKDIR /build

# 设置环境变量
ENV GOPROXY=${GOPROXY}
ENV CGO_ENABLED=0
ENV GOOS=linux
ENV GOARCH=amd64

# 复制 go.mod 和 go.sum (利用 Docker 缓存层)
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 获取构建信息
RUN BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ) && \
    GIT_COMMIT=$(git rev-parse HEAD 2>/dev/null || echo "unknown") && \
    GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown") && \
    BUILD_USER=$(whoami 2>/dev/null || echo "docker") && \
    BUILD_HOST=$(hostname 2>/dev/null || echo "docker") && \
    GO_VERSION=$(go version | awk '{print $3}') && \
    \
    # 构建应用
    go build -v \
        -ldflags "-s -w \
            -X cmdb/version.Version=${VERSION} \
            -X cmdb/version.GitCommit=${GIT_COMMIT} \
            -X cmdb/version.GitBranch=${GIT_BRANCH} \
            -X cmdb/version.BuildTime=${BUILD_TIME} \
            -X cmdb/version.BuildUser=${BUILD_USER} \
            -X cmdb/version.BuildHost=${BUILD_HOST} \
            -X cmdb/version.GoVersion=${GO_VERSION}" \
        -o cmdb .

# -----------------------------------------------------------------------------
# 运行阶段
# -----------------------------------------------------------------------------
FROM alpine:3.21

# 安装必要的运行时依赖
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories  && apk add --no-cache \
    ca-certificates \
    tzdata \
    curl \
    && rm -rf /var/cache/apk/*

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非 root 用户
RUN addgroup -g 1000 cmdb && \
    adduser -D -s /bin/sh -u 1000 -G cmdb cmdb

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /build/cmdb .

# 复制配置文件模板 (如果存在)
COPY --from=builder /build/app.ini.example ./app.ini.example

# 创建必要的目录
RUN mkdir -p /app/logs /app/data && \
    chown -R cmdb:cmdb /app

# 切换到非 root 用户
USER root

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 设置启动命令
ENTRYPOINT ["./cmdb"]

# 默认参数 (可以被 docker run 覆盖)
CMD ["-f", "app.ini"]

# -----------------------------------------------------------------------------
# 元数据标签
# -----------------------------------------------------------------------------
LABEL maintainer="CMDB Team"
LABEL description="CMDB - 配置管理数据库系统"
LABEL version="${VERSION}"
LABEL org.opencontainers.image.title="CMDB"
LABEL org.opencontainers.image.description="配置管理数据库系统"
LABEL org.opencontainers.image.version="${VERSION}"
LABEL org.opencontainers.image.vendor="CMDB Team"
LABEL org.opencontainers.image.licenses="MIT"
