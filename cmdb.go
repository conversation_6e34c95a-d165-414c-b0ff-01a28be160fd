package main

import (
	"cmdb/app"
	"cmdb/app/api"
	"cmdb/cmd/admin"
	asynctask "cmdb/cmd/async-task"
	"flag"
	"fmt"
	"log/slog"
	"os"
	"os/signal"
	"path/filepath"
	"time"
)

// init 函数在程序启动时执行，用于确保当前工作目录与程序所在目录一致
func init() {
	// 获取当前工作目录
	pwd, _ := os.Getwd()
	// 获取程序所在目录
	execDir, err := filepath.Abs(filepath.Dir(os.Args[0]))
	if err != nil {
		panic(err)
	}
	// 如果当前工作目录和程序所在目录相同，则直接返回
	if pwd == execDir {
		return
	}
	// 切换工作目录到程序所在目录，如果失败则抛出异常
	if err := os.Chdir(execDir); err != nil {
		panic(err)
	}
}

// AppConfig 应用配置参数结构体
type AppConfig struct {
	// 配置文件相关
	ConfigFile string

	// 操作模式参数（互斥）
	DefaultConfig bool // 打印默认配置
	SyncDB        bool // 同步数据库表结构
	CreateAdmin   bool // 创建超级管理员
	AsyncTask     bool // 运行异步任务
	Version       bool // 显示版本信息
	Help          bool // 显示帮助信息
}

// validateConfig 验证配置参数
func (c *AppConfig) validateConfig() error {
	// 验证配置文件路径
	if c.ConfigFile != "" {
		if _, err := os.Stat(c.ConfigFile); os.IsNotExist(err) {
			return fmt.Errorf("配置文件不存在: %s", c.ConfigFile)
		}

		// 检查文件是否可读
		file, err := os.Open(c.ConfigFile)
		if err != nil {
			return fmt.Errorf("配置文件无法读取: %s, 错误: %v", c.ConfigFile, err)
		}
		file.Close()
	}

	// 检查互斥参数
	mutexCount := 0
	if c.DefaultConfig {
		mutexCount++
	}
	if c.SyncDB {
		mutexCount++
	}
	if c.CreateAdmin {
		mutexCount++
	}
	if c.AsyncTask {
		mutexCount++
	}
	if c.Version {
		mutexCount++
	}

	if mutexCount > 1 {
		return fmt.Errorf("以下参数不能同时使用: -default-config, -syncdb, -createAdmin, -async-task, -version")
	}

	return nil
}

// parseFlags 解析命令行参数
func parseFlags() (*AppConfig, error) {
	config := &AppConfig{}

	// 配置文件参数
	flag.StringVar(&config.ConfigFile, "f", "app.ini",
		"指定配置文件路径 (默认: app.ini)\n"+
			"    配置文件必须存在且可读")

	flag.StringVar(&config.ConfigFile, "config", "app.ini",
		"指定配置文件路径 (同 -f)")

	// 操作模式参数
	flag.BoolVar(&config.DefaultConfig, "default-config", false,
		"打印默认配置到标准输出\n"+
			"    用于生成初始配置文件模板")

	flag.BoolVar(&config.SyncDB, "syncdb", false,
		"同步数据库表结构\n"+
			"    根据模型定义创建或更新数据库表")

	flag.BoolVar(&config.CreateAdmin, "createAdmin", false,
		"创建超级管理员用户\n"+
			"    交互式创建具有所有权限的管理员账户")

	flag.BoolVar(&config.AsyncTask, "async-task", false,
		"运行异步任务处理器\n"+
			"    启动后台任务处理服务")

	flag.BoolVar(&config.Version, "version", false,
		"显示版本信息")

	flag.BoolVar(&config.Version, "v", false,
		"显示版本信息 (同 -version)")

	flag.BoolVar(&config.Help, "help", false,
		"显示此帮助信息")

	flag.BoolVar(&config.Help, "h", false,
		"显示此帮助信息 (同 -help)")

	// 自定义Usage函数
	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "CMDB - 配置管理数据库系统\n\n")
		fmt.Fprintf(os.Stderr, "用法: %s [选项]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "配置选项:\n")
		fmt.Fprintf(os.Stderr, "  -f, -config <文件>     指定配置文件路径 (默认: app.ini)\n\n")
		fmt.Fprintf(os.Stderr, "操作模式 (以下选项互斥，只能选择一个):\n")
		fmt.Fprintf(os.Stderr, "  -default-config        打印默认配置模板\n")
		fmt.Fprintf(os.Stderr, "  -syncdb               同步数据库表结构\n")
		fmt.Fprintf(os.Stderr, "  -createAdmin          创建超级管理员\n")
		fmt.Fprintf(os.Stderr, "  -async-task           运行异步任务处理器\n")
		fmt.Fprintf(os.Stderr, "  -v, -version          显示版本信息\n\n")
		fmt.Fprintf(os.Stderr, "其他选项:\n")
		fmt.Fprintf(os.Stderr, "  -h, -help             显示此帮助信息\n\n")
		fmt.Fprintf(os.Stderr, "示例:\n")
		fmt.Fprintf(os.Stderr, "  %s                    启动CMDB服务 (使用默认配置)\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -f /etc/cmdb.ini   使用指定配置文件启动服务\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -syncdb            同步数据库表结构\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -createAdmin       创建管理员用户\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -default-config > cmdb.ini  生成配置文件模板\n", os.Args[0])
	}

	// 解析参数
	flag.Parse()

	// 如果请求帮助，显示帮助信息并退出
	if config.Help {
		flag.Usage()
		os.Exit(0)
	}

	// 验证参数
	if err := config.validateConfig(); err != nil {
		return nil, fmt.Errorf("参数验证失败: %v", err)
	}

	return config, nil
}

// handleError 统一错误处理
func handleError(msg string, err error, exitCode int) {
	if err != nil {
		slog.Error(msg, "error", err)
	} else {
		slog.Error(msg)
	}
	os.Exit(exitCode)
}

// main 函数是程序的入口点
func main() {
	// 记录程序启动时间
	bootTime := time.Now()

	// 解析命令行参数
	config, err := parseFlags()
	if err != nil {
		fmt.Fprintf(os.Stderr, "错误: %v\n", err)
		fmt.Fprintf(os.Stderr, "使用 -h 或 -help 查看帮助信息\n")
		os.Exit(1)
	}

	// 处理版本信息
	if config.Version {
		fmt.Printf("CMDB 版本: 3.1.0\n")
		fmt.Printf("构建时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
		fmt.Printf("Go 版本: %s\n", "go1.24+")
		os.Exit(0)
	}

	// 处理打印默认配置
	if config.DefaultConfig {
		app.PrintDefaultConfig()
		os.Exit(0)
	}

	// 处理创建超级管理员
	if config.CreateAdmin {
		slog.Info("开始创建超级管理员", "config_file", config.ConfigFile)
		err := admin.CreateAdminUser(config.ConfigFile)
		if err != nil {
			handleError("创建管理用户失败", err, 1)
		}
		slog.Info("超级管理员创建成功")
		os.Exit(0)
	}

	// 处理同步数据库表结构
	if config.SyncDB {
		slog.Info("开始同步数据库表结构", "config_file", config.ConfigFile)
		err := admin.SyncDB(config.ConfigFile)
		if err != nil {
			handleError("同步表结构失败", err, 1)
		}
		slog.Info("数据库表结构同步成功")
		os.Exit(0)
	}

	// 处理异步任务
	if config.AsyncTask {
		slog.Info("启动异步任务处理器", "config_file", config.ConfigFile)
		asynctask.Run(config.ConfigFile)
		os.Exit(0)
	}

	// 默认启动HTTP服务模式
	slog.Info("启动CMDB服务", "config_file", config.ConfigFile, "pid", os.Getpid())

	// 初始化应用
	err = app.NewApp(config.ConfigFile)
	if err != nil {
		handleError("应用初始化失败", err, 1)
	}

	// 生成HTTP路由
	router, err := api.GenRoute()
	if err != nil {
		handleError("构建HTTP路由失败", err, 1)
	}

	// 启动HTTP服务
	err = app.StartHttp(router)
	if err != nil {
		handleError("启动HTTP服务失败", err, 1)
	}

	app.Log().Info("CMDB服务启动成功", "config_file", config.ConfigFile)

	// 监听系统中断信号，以便优雅地关闭应用
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt)
	<-quit

	app.Log().Info("收到关闭信号，开始优雅关闭服务...")

	// 停止HTTP服务
	err = app.StopHttp()
	if err != nil {
		app.Log().Error("停止HTTP服务失败", "error", err)
		os.Exit(1)
	}

	// 记录程序关闭时间和运行时长
	app.Log().Info("CMDB服务已关闭", "运行时长", time.Since(bootTime).String())
}
