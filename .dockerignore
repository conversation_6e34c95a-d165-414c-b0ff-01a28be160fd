# =============================================================================
# Docker 忽略文件
# =============================================================================
# 
# 这个文件定义了在构建 Docker 镜像时应该忽略的文件和目录
# 可以减少构建上下文的大小，提高构建速度
#
# =============================================================================

# -----------------------------------------------------------------------------
# 构建产物
# -----------------------------------------------------------------------------
build/
dist/
*.exe
cmdb
cmdb-*

# -----------------------------------------------------------------------------
# 开发工具和配置
# -----------------------------------------------------------------------------
.git/
.gitignore
.gitattributes
.github/
.vscode/
.idea/
*.swp
*.swo
*~

# -----------------------------------------------------------------------------
# 文档和说明
# -----------------------------------------------------------------------------
README.md
CHANGELOG.md
LICENSE
docs/
*.md

# -----------------------------------------------------------------------------
# 测试和覆盖率
# -----------------------------------------------------------------------------
coverage.out
coverage.html
*.test
test/
*_test.go

# -----------------------------------------------------------------------------
# 日志和临时文件
# -----------------------------------------------------------------------------
logs/
*.log
tmp/
temp/
.tmp/

# -----------------------------------------------------------------------------
# 依赖和缓存
# -----------------------------------------------------------------------------
vendor/
node_modules/
.cache/

# -----------------------------------------------------------------------------
# 配置文件 (保留模板)
# -----------------------------------------------------------------------------
app.ini
config.yaml
config.json
!app.ini.example
!config.yaml.example
!config.json.example

# -----------------------------------------------------------------------------
# 数据文件
# -----------------------------------------------------------------------------
data/
*.db
*.sqlite
*.sqlite3

# -----------------------------------------------------------------------------
# 操作系统文件
# -----------------------------------------------------------------------------
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# -----------------------------------------------------------------------------
# 其他
# -----------------------------------------------------------------------------
Makefile
build.sh
docker-compose.yml
docker-compose.yaml
.dockerignore
Dockerfile*
