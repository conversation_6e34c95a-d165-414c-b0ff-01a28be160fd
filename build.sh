#!/bin/bash

# CMDB 构建脚本
# 使用编译时变量注入版本信息

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_tools() {
    print_info "检查构建工具..."
    
    if ! command -v go &> /dev/null; then
        print_error "Go 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        print_warning "Git 未安装，将使用默认值"
        USE_GIT=false
    else
        USE_GIT=true
    fi
    
    print_success "工具检查完成"
}

# 获取版本信息
get_version_info() {
    print_info "收集版本信息..."
    
    # 版本号 - 可以通过环境变量 VERSION 指定，否则使用 Git 标签或默认值
    if [ -n "$VERSION" ]; then
        VERSION_VAR="$VERSION"
    elif [ "$USE_GIT" = true ] && git describe --tags --exact-match HEAD 2>/dev/null; then
        VERSION_VAR=$(git describe --tags --exact-match HEAD)
    elif [ "$USE_GIT" = true ] && git describe --tags 2>/dev/null; then
        VERSION_VAR=$(git describe --tags)
    else
        VERSION_VAR="dev-$(date +%Y%m%d)"
    fi
    
    # Git 信息
    if [ "$USE_GIT" = true ]; then
        GIT_COMMIT=$(git rev-parse HEAD 2>/dev/null || echo "unknown")
        GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
        
        # 检查是否有未提交的更改
        if ! git diff-index --quiet HEAD -- 2>/dev/null; then
            GIT_COMMIT="${GIT_COMMIT}-dirty"
        fi
    else
        GIT_COMMIT="unknown"
        GIT_BRANCH="unknown"
    fi
    
    # 构建信息
    BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)
    BUILD_USER=$(whoami 2>/dev/null || echo "unknown")
    BUILD_HOST=$(hostname 2>/dev/null || echo "unknown")
    GO_VERSION=$(go version | awk '{print $3}' 2>/dev/null || echo "unknown")
    
    print_success "版本信息收集完成"
    echo "  版本号: $VERSION_VAR"
    echo "  Git提交: $GIT_COMMIT"
    echo "  Git分支: $GIT_BRANCH"
    echo "  构建时间: $BUILD_TIME"
    echo "  构建者: $BUILD_USER"
    echo "  构建主机: $BUILD_HOST"
    echo "  Go版本: $GO_VERSION"
}

# 构建应用
build_app() {
    print_info "开始构建应用..."
    
    # 构建标志
    LDFLAGS="-s -w"
    LDFLAGS="$LDFLAGS -X cmdb/version.Version=$VERSION_VAR"
    LDFLAGS="$LDFLAGS -X cmdb/version.GitCommit=$GIT_COMMIT"
    LDFLAGS="$LDFLAGS -X cmdb/version.GitBranch=$GIT_BRANCH"
    LDFLAGS="$LDFLAGS -X cmdb/version.BuildTime=$BUILD_TIME"
    LDFLAGS="$LDFLAGS -X cmdb/version.BuildUser=$BUILD_USER"
    LDFLAGS="$LDFLAGS -X cmdb/version.BuildHost=$BUILD_HOST"
    LDFLAGS="$LDFLAGS -X cmdb/version.GoVersion=$GO_VERSION"
    
    # 输出文件名
    OUTPUT_NAME="cmdb"
    if [ -n "$OUTPUT" ]; then
        OUTPUT_NAME="$OUTPUT"
    fi
    
    # 构建命令
    BUILD_CMD="go build -ldflags \"$LDFLAGS\" -o $OUTPUT_NAME cmdb.go"
    
    print_info "执行构建命令: $BUILD_CMD"
    
    if eval $BUILD_CMD; then
        print_success "构建完成: $OUTPUT_NAME"
        
        # 显示文件信息
        if [ -f "$OUTPUT_NAME" ]; then
            FILE_SIZE=$(ls -lh "$OUTPUT_NAME" | awk '{print $5}')
            print_info "文件大小: $FILE_SIZE"
        fi
    else
        print_error "构建失败"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "CMDB 构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -v, --version VER   指定版本号"
    echo ""
    echo "环境变量:"
    echo "  VERSION             版本号"
    echo "  OUTPUT              输出文件名"
    echo ""
    echo "示例:"
    echo "  $0                  # 普通构建"
    echo "  $0 -v 1.0.0         # 指定版本号构建"
}

# 主函数
main() {
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--version)
                VERSION="$2"
                shift 2
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 执行构建流程
    check_tools
    get_version_info
    build_app
    
    print_success "构建任务完成！"
}

# 执行主函数
main "$@"
